"use client";

import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/common/ui/button";
import { Input } from "@/components/common/ui/input";
import { Separator } from "@/components/common/ui/separator";
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>ist,
  Ta<PERSON><PERSON>rigger,
  TabsContent,
} from "@/components/common/ui/tabs";
import {
  Search,
  Calendar,
  DollarSign,
  Clock,
  MapPin,
  Package,
  Shield,
} from "lucide-react";
import { useAuth } from "@/hooks/useAuth";
import { Listing } from "@/layouts/dashboard/details/basic";

// Mock data for tenders
const mockTenders = [
  {
    id: "1",
    subHeading: "Sub Heading",
    title: "Main Heading",
    description:
      'Thought: The user simply said "more." This is a follow-up to our previous conversation where I provided five paraphrases of "Thought for 55 seconds." The most logical and helpful interpretation of "more" in this context is that the user wants more paraphrases of the same sentence. They are likely looking for a continuation of the list I just provided.\n\nI need to come up with another five unique ways to paraphrase "Thought for 55 seconds." I should try to use different vocabulary and sentence structures than the ones I\'ve already provide...',
    tags: [
      { label: "Food Supply", variant: "secondary" as const, icon: Package },
      { label: "Dar es Salaam", variant: "outline" as const, icon: MapPin },
      {
        label: "Due: Nov 15, 2025",
        variant: "outline" as const,
        icon: Calendar,
      },
      { label: "$2500", variant: "outline" as const, icon: DollarSign },
      { label: "2 hours ago", variant: "outline" as const, icon: Clock },
    ],
    actions: (
      <div className="flex items-center gap-2">
        <Button size="sm" className="bg-green-600 hover:bg-green-700">
          Open
        </Button>
      </div>
    ),
  },
  {
    id: "2",
    subHeading: "Sub Heading",
    title: "Main Heading",
    description:
      'Thought: The user simply said "more." This is a follow-up to our previous conversation where I provided five paraphrases of "Thought for 55 seconds." The most logical and helpful interpretation of "more" in this context is that the user wants more paraphrases of the same sentence. They are likely looking for a continuation of the list I just provided.\n\nI need to come up with another five unique ways to paraphrase "Thought for 55 seconds." I should try to use different vocabulary and sentence structures than the ones I\'ve already provide...',
    tags: [
      { label: "Food Supply", variant: "secondary" as const, icon: Package },
      { label: "Dar es Salaam", variant: "outline" as const, icon: MapPin },
      {
        label: "Due: Nov 15, 2025",
        variant: "outline" as const,
        icon: Calendar,
      },
      { label: "$2500", variant: "outline" as const, icon: DollarSign },
      { label: "2 hours ago", variant: "outline" as const, icon: Clock },
    ],
    actions: (
      <div className="flex items-center gap-2">
        <Button
          size="sm"
          variant="outline"
          className="text-green-600 border-green-600 hover:bg-green-50"
        >
          Unlock to View
        </Button>
      </div>
    ),
  },
];

export function ClientDashboardContainer() {
  const [searchTerm, setSearchTerm] = useState("");
  const [activeTab, setActiveTab] = useState("recent");
  const { user } = useAuth();

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return "Good morning";
    if (hour < 17) return "Good afternoon";
    return "Good evening";
  };

  return (
    <div className="min-h-screen bg-background relative">
      {/* Header */}
      <div className="flex items-center justify-between p-6 pb-4">
        <div>
          <h1 className="text-2xl font-semibold text-foreground">
            {getGreeting()} {user?.firstName} {user?.lastName?.charAt(0)}.
          </h1>
        </div>
        <div className="relative w-80">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="px-6">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="w-max bg-transparent border-b border-border rounded-none h-auto p-0">
            <TabsTrigger
              value="recent"
              className="bg-transparent border-b-2 border-transparent data-[state=active]:border-foreground data-[state=active]:bg-transparent rounded-none px-0 pb-2 mr-6"
            >
              Recent
            </TabsTrigger>
            <TabsTrigger
              value="saved"
              className="bg-transparent border-b-2 border-transparent data-[state=active]:border-foreground data-[state=active]:bg-transparent rounded-none px-0 pb-2 mr-6"
            >
              Saved
            </TabsTrigger>
            <TabsTrigger
              value="viewed"
              className="bg-transparent border-b-2 border-transparent data-[state=active]:border-foreground data-[state=active]:bg-transparent rounded-none px-0 pb-2"
            >
              Viewed
            </TabsTrigger>
          </TabsList>

          <Separator />

          {/* Tab Content */}
          <TabsContent value="recent" className="mt-6">
            <div className="grid gap-6">
              {mockTenders.map((tender) => (
                <Listing.WideCard
                  key={tender.id}
                  subHeading={tender.subHeading}
                  title={tender.title}
                  description={tender.description}
                  tags={tender.tags}
                  actions={tender.actions}
                />
              ))}
            </div>
          </TabsContent>

          <TabsContent value="saved" className="mt-6">
            <div className="text-center py-12">
              <p className="text-muted-foreground">No saved items yet</p>
            </div>
          </TabsContent>

          <TabsContent value="viewed" className="mt-6">
            <div className="text-center py-12">
              <p className="text-muted-foreground">No viewed items yet</p>
            </div>
          </TabsContent>
        </Tabs>
      </div>

      {/* Upgrade Banner */}
      <div className="absolute bottom-0 left-0 right-0 bg-green-100 border-t border-green-200 p-4">
        <div className="flex items-center justify-between max-w-7xl mx-auto">
          <div className="flex items-center gap-3">
            <div className="bg-green-600 p-2 rounded">
              <Shield className="h-5 w-5 text-white" />
            </div>
            <div>
              <p className="font-medium text-green-800">Upgrade to Quick Win</p>
              <p className="text-sm text-green-600">
                Unlock 200+ features, integrations, and advanced reporting.
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              className="border-green-600 text-green-600 hover:bg-green-50"
            >
              Upgrade Now
            </Button>
            <Button variant="ghost" size="sm" className="text-green-600">
              ×
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
